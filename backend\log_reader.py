import sqlite3
import time
from datetime import datetime

DB_PATH = "database.db"  # Adjust path if needed

def read_live_logs():
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    last_seen = None

    print("[Live Log Watcher Started]")
    while True:
        query = "SELECT token, content, updated_at FROM clipboards ORDER BY updated_at DESC LIMIT 1"
        cursor.execute(query)
        row = cursor.fetchone()

        if row:
            token, content, updated_at = row
            updated_at = datetime.fromisoformat(updated_at)

            if last_seen is None or updated_at > last_seen:
                print(f"[{updated_at.strftime('%Y-%m-%d %H:%M:%S')}] Token: {token} -> {content}")
                last_seen = updated_at

        time.sleep(2)

if __name__ == "__main__":
    read_live_logs()
