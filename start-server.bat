@echo off
echo 🚀 Starting ClipIt Development Server
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found. Please install Python first.
    echo 💡 Download from: https://python.org
    pause
    exit /b 1
)

REM Try HTTPS server first
echo 🔐 Attempting to start HTTPS server...
python server.py

REM If that fails, fallback to simple HTTP
if %errorlevel% neq 0 (
    echo.
    echo ⚠️  HTTPS server failed. Starting simple HTTP server...
    echo 🌐 Open: http://localhost:8000
    echo ⚠️  Note: Clipboard API may not work without HTTPS
    python -m http.server 8000
)

pause
