from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, func
from sqlalchemy.orm import sessionmaker, declarative_base

# Database setup
DATABASE_URL = "sqlite:///./database.db"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
Session = sessionmaker(bind=engine)
Base = declarative_base()

class Clipboard(Base):
    __tablename__ = "clipboards"
    id = Column(Integer, primary_key=True, index=True)
    token = Column(String(50), unique=True, nullable=False)
    content = Column(Text)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

Base.metadata.create_all(bind=engine)

# App setup
app = FastAPI()

# CORS (allow frontend access)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Change to specific domain in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ClipboardSchema(BaseModel):
    token: str
    content: str

@app.get("/clipboards")
def get_clipboard(token: str):
    db = Session()
    record = db.query(Clipboard).filter(Clipboard.token == token).first()
    db.close()
    if record:
        return [{
            "id": record.id,
            "token": record.token,
            "content": record.content,
            "updated_at": record.updated_at
        }]
    return []

@app.post("/clipboards")
def create_clipboard(data: ClipboardSchema):
    db = Session()
    existing = db.query(Clipboard).filter(Clipboard.token == data.token).first()
    if existing:
        db.close()
        raise HTTPException(status_code=400, detail="Token already exists")
    new_record = Clipboard(token=data.token, content=data.content)
    db.add(new_record)
    db.commit()
    db.refresh(new_record)
    db.close()
    return {"id": new_record.id}

@app.patch("/clipboards/{id}")
def update_clipboard(id: int, data: ClipboardSchema):
    db = Session()
    record = db.query(Clipboard).filter(Clipboard.id == id).first()
    if not record:
        db.close()
        raise HTTPException(status_code=404, detail="Record not found")
    record.content = data.content
    db.commit()
    db.refresh(record)
    db.close()
    return {"success": True}
