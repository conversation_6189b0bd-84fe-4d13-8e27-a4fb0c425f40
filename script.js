// ClipIt - Main Application Logic
console.log("here3");

class ClipIt {
    constructor() {
        this.currentToken = '';
        this.isConnected = false;
        this.subscription = null;
        this.lastClipboardContent = '';
        this.preventFeedbackLoop = false;
        
        this.initializeElements();
        this.attachEventListeners();
        this.loadStoredData();
        this.checkClipboardPermissions();
        
        console.log('ClipIt initialized');
    }

    initializeElements() {
        // Get DOM elements
        this.elements = {
            syncToken: document.getElementById('syncToken'),
            startSyncBtn: document.getElementById('startSyncBtn'),
            statusText: document.getElementById('statusText'),
            statusDot: document.getElementById('statusDot'),
            clipboardContent: document.getElementById('clipboardContent'),
            readClipboardBtn: document.getElementById('readClipboardBtn'),
            writeClipboardBtn: document.getElementById('writeClipboardBtn'),
            copyBtn: document.getElementById('copyBtn'),
            clearBtn: document.getElementById('clearBtn'),
            logContainer: document.getElementById('logContainer'),
            clearLogsBtn: document.getElementById('clearLogsBtn')
        };
    }

    attachEventListeners() {
        // Sync controls
        this.elements.startSyncBtn.addEventListener('click', () => this.toggleSync());
        this.elements.syncToken.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.toggleSync();
        });

        // Clipboard controls
        this.elements.readClipboardBtn.addEventListener('click', () => this.readClipboard());
        this.elements.writeClipboardBtn.addEventListener('click', () => this.writeClipboard());
        this.elements.copyBtn.addEventListener('click', () => this.copyText());
        this.elements.clearBtn.addEventListener('click', () => this.clearContent());

        // Log controls
        this.elements.clearLogsBtn.addEventListener('click', () => this.clearLogs());

        // Auto-sync on content change (when connected)
        this.elements.clipboardContent.addEventListener('input', () => {
            if (this.isConnected && !this.preventFeedbackLoop) {
                this.debounce(() => this.syncToDatabase(), 1000);
            }
        });

        // Check for clipboard changes periodically when connected
        setInterval(() => {
            if (this.isConnected) {
                this.checkClipboardChanges();
            }
        }, 2000);
    }

    loadStoredData() {
        // Load stored token and logs
        const storedToken = localStorage.getItem('clipit_token');
        if (storedToken) {
            this.elements.syncToken.value = storedToken;
        }

        const storedLogs = localStorage.getItem('clipit_logs');
        if (storedLogs) {
            try {
                const logs = JSON.parse(storedLogs);
                logs.forEach(log => this.addLog(log.message, log.type, false));
            } catch (e) {
                console.error('Failed to load stored logs:', e);
            }
        }
    }

    async checkClipboardPermissions() {
        try {
            const permission = await navigator.permissions.query({ name: 'clipboard-read' });
            if (permission.state === 'denied') {
                this.addLog('Clipboard read permission denied. Some features may not work.', 'error');
            }
        } catch (error) {
            console.log('Clipboard permissions check not supported');
        }
    }

    async toggleSync() {
        console.log("clicked")
        if (this.isConnected) {
            await this.stopSync();
        } else {
            await this.startSync();
        }
    }

    async startSync() {
        const token = this.elements.syncToken.value.trim();
        
        if (!token) {
            this.addLog('Please enter a sync token', 'error');
            return;
        }

        if (!window.clipitUtils.isValidToken(token)) {
            this.addLog('Invalid token format. Use 3-50 characters (letters, numbers, _, -)', 'error');
            return;
        }

        if (!window.clipitUtils.isConfigured()) {
            this.addLog('Supabase not configured. Please update supabase.js with your credentials.', 'error');
            return;
        }

        try {
            this.updateStatus('connecting', 'Connecting...');
            this.elements.startSyncBtn.disabled = true;

            // Store token
            this.currentToken = token;
            localStorage.setItem('clipit_token', token);

            // Load initial content from database
            const data = await window.clipitDB.getClipboard(token);
            if (data && data.content) {
                this.elements.clipboardContent.value = data.content;
                this.lastClipboardContent = data.content;
                this.addLog(`Loaded existing content for token: ${token}`, 'success');
            } else {
                this.addLog(`No existing content found for token: ${token}`, 'info');
            }

            // Subscribe to real-time updates
            this.subscription = window.clipitDB.subscribeToChanges(token, (payload) => {
                this.handleRealtimeUpdate(payload);
            });

            this.isConnected = true;
            this.updateStatus('online', 'Connected');
            this.elements.startSyncBtn.textContent = 'Stop Sync';
            this.elements.startSyncBtn.disabled = false;
            this.elements.syncToken.disabled = true;

            this.addLog(`Started syncing with token: ${token}`, 'success');

        } catch (error) {
            console.error('Failed to start sync:', error);
            this.addLog(`Failed to start sync: ${error.message}`, 'error');
            this.updateStatus('offline', 'Connection failed');
            this.elements.startSyncBtn.disabled = false;
        }
    }

    async stopSync() {
        try {
            if (this.subscription) {
                window.clipitDB.unsubscribe(this.subscription);
                this.subscription = null;
            }

            this.isConnected = false;
            this.currentToken = '';
            this.updateStatus('offline', 'Not connected');
            this.elements.startSyncBtn.textContent = 'Start Sync';
            this.elements.syncToken.disabled = false;

            this.addLog('Stopped syncing', 'info');

        } catch (error) {
            console.error('Error stopping sync:', error);
            this.addLog(`Error stopping sync: ${error.message}`, 'error');
        }
    }

    handleRealtimeUpdate(payload) {
        if (payload.new && payload.new.content !== undefined) {
            const newContent = payload.new.content;
            
            // Prevent feedback loop
            if (newContent === this.lastClipboardContent) {
                return;
            }

            this.preventFeedbackLoop = true;
            this.elements.clipboardContent.value = newContent;
            this.lastClipboardContent = newContent;

            // Update system clipboard
            this.writeToSystemClipboard(newContent);
            
            this.addLog('Content updated from remote', 'success');
            
            setTimeout(() => {
                this.preventFeedbackLoop = false;
            }, 1000);
        }
    }

    async syncToDatabase() {
        if (!this.isConnected || this.preventFeedbackLoop) return;

        const content = this.elements.clipboardContent.value;
        
        if (content === this.lastClipboardContent) return;

        try {
            await window.clipitDB.setClipboard(this.currentToken, content);
            this.lastClipboardContent = content;
            this.addLog('Content synced to database', 'success');
        } catch (error) {
            console.error('Failed to sync to database:', error);
            this.addLog(`Failed to sync: ${error.message}`, 'error');
        }
    }

    async checkClipboardChanges() {
        try {
            const clipboardText = await navigator.clipboard.readText();
            
            if (clipboardText !== this.lastClipboardContent && 
                clipboardText !== this.elements.clipboardContent.value) {
                
                this.preventFeedbackLoop = true;
                this.elements.clipboardContent.value = clipboardText;
                this.lastClipboardContent = clipboardText;
                
                // Sync to database
                await this.syncToDatabase();
                this.addLog('Clipboard change detected and synced', 'info');
                
                setTimeout(() => {
                    this.preventFeedbackLoop = false;
                }, 1000);
            }
        } catch (error) {
            // Silently handle clipboard read errors (common when tab is not active)
        }
    }

    async readClipboard() {
        try {
            const text = await navigator.clipboard.readText();
            this.elements.clipboardContent.value = text;
            this.addLog('Clipboard content read successfully', 'success');
            
            if (this.isConnected) {
                await this.syncToDatabase();
            }
        } catch (error) {
            console.error('Failed to read clipboard:', error);
            this.addLog('Failed to read clipboard. Make sure to grant permission.', 'error');
        }
    }

    async writeClipboard() {
        const content = this.elements.clipboardContent.value;
        await this.writeToSystemClipboard(content);
    }

    async writeToSystemClipboard(content) {
        try {
            await navigator.clipboard.writeText(content);
            this.addLog('Content written to clipboard', 'success');
        } catch (error) {
            console.error('Failed to write to clipboard:', error);
            this.addLog('Failed to write to clipboard', 'error');
        }
    }

    copyText() {
        this.elements.clipboardContent.select();
        document.execCommand('copy');
        this.addLog('Text copied to clipboard', 'success');
    }

    clearContent() {
        this.elements.clipboardContent.value = '';
        this.addLog('Content cleared', 'info');
        
        if (this.isConnected) {
            this.syncToDatabase();
        }
    }

    updateStatus(status, text) {
        this.elements.statusText.textContent = text;
        this.elements.statusDot.className = `status-dot ${status}`;
    }

    addLog(message, type = 'info', store = true) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('p');
        logEntry.className = `log-entry ${type}`;
        logEntry.textContent = `[${timestamp}] ${message}`;
        
        this.elements.logContainer.appendChild(logEntry);
        this.elements.logContainer.scrollTop = this.elements.logContainer.scrollHeight;

        // Store logs in localStorage
        if (store) {
            this.storeLogs();
        }
    }

    storeLogs() {
        const logs = Array.from(this.elements.logContainer.children).map(entry => ({
            message: entry.textContent,
            type: entry.className.split(' ').find(cls => ['success', 'error', 'info'].includes(cls)) || 'info'
        }));
        
        // Keep only last 50 logs
        const recentLogs = logs.slice(-50);
        localStorage.setItem('clipit_logs', JSON.stringify(recentLogs));
    }

    clearLogs() {
        this.elements.logContainer.innerHTML = '<p class="log-entry">Logs cleared</p>';
        localStorage.removeItem('clipit_logs');
    }

    debounce(func, wait) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(func, wait);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.clipit = new ClipIt();
});