/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main content */
main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* Card-like sections */
.sync-section,
.clipboard-section,
.logs-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

/* Sync section */
.input-group {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.input-group label {
    font-weight: 600;
    min-width: 100px;
}

.input-group input {
    flex: 1;
    min-width: 200px;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Status indicator */
.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.status-dot.offline {
    background-color: #dc3545;
}

.status-dot.online {
    background-color: #28a745;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.status-dot.connecting {
    background-color: #ffc107;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Clipboard section */
.clipboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.clipboard-header h3 {
    color: #333;
    font-size: 1.3rem;
}

.clipboard-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    resize: vertical;
    min-height: 150px;
    margin-bottom: 15px;
    transition: border-color 0.3s ease;
}

textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
}

.btn-secondary:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Logs section */
.logs-section h3 {
    margin-bottom: 15px;
    color: #333;
}

.log-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 15px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

.log-entry {
    margin-bottom: 8px;
    padding: 4px 0;
    border-bottom: 1px solid #e9ecef;
}

.log-entry:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.log-entry.success {
    color: #28a745;
}

.log-entry.error {
    color: #dc3545;
}

.log-entry.info {
    color: #17a2b8;
}

/* Footer */
footer {
    text-align: center;
    margin-top: 30px;
    color: white;
    opacity: 0.8;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .input-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .input-group label {
        min-width: auto;
    }
    
    .clipboard-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .clipboard-actions {
        justify-content: center;
    }
}

/* Scrollbar styling */
.log-container::-webkit-scrollbar {
    width: 6px;
}

.log-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}