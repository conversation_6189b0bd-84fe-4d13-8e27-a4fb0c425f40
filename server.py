#!/usr/bin/env python3
"""
Simple HTTPS server for ClipIt development
Run this to serve the app over HTTPS for clipboard API access

Usage:
    python server.py

Then open: https://localhost:8443
"""

import http.server
import ssl
import socketserver
import os

PORT = 8443

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers for development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        # Add security headers for clipboard API
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        super().end_headers()

    def do_OPTIONS(self):
        # Handle preflight requests
        self.send_response(200)
        self.end_headers()

def create_self_signed_cert():
    """Create a self-signed certificate for development"""
    try:
        import subprocess
        
        # Check if certificate already exists
        if os.path.exists('server.crt') and os.path.exists('server.key'):
            print("✅ Using existing SSL certificate")
            return True
            
        print("🔐 Creating self-signed SSL certificate...")
        
        # Create self-signed certificate
        subprocess.run([
            'openssl', 'req', '-x509', '-newkey', 'rsa:4096', '-keyout', 'server.key',
            '-out', 'server.crt', '-days', '365', '-nodes', '-subj',
            '/C=US/ST=State/L=City/O=Organization/CN=localhost'
        ], check=True, capture_output=True)
        
        print("✅ SSL certificate created successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create SSL certificate: {e}")
        print("💡 Make sure OpenSSL is installed and available in PATH")
        return False
    except FileNotFoundError:
        print("❌ OpenSSL not found. Please install OpenSSL to create SSL certificates.")
        print("💡 Alternative: Use a development server like 'python -m http.server' with localhost")
        return False

def main():
    print("🚀 Starting ClipIt HTTPS Development Server")
    print(f"📁 Serving files from: {os.getcwd()}")
    
    # Create SSL certificate if needed
    if not create_self_signed_cert():
        print("\n⚠️  Running without HTTPS. Clipboard API may not work.")
        print("🌐 Starting HTTP server on port 8000...")
        
        with socketserver.TCPServer(("", 8000), MyHTTPRequestHandler) as httpd:
            print(f"🔗 Open: http://localhost:8000")
            print("⚠️  Note: Clipboard API requires HTTPS or localhost")
            httpd.serve_forever()
        return
    
    # Setup HTTPS server
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        # Create SSL context
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain('server.crt', 'server.key')
        
        # Wrap socket with SSL
        httpd.socket = context.wrap_socket(httpd.socket, server_side=True)
        
        print(f"🔗 HTTPS Server running at: https://localhost:{PORT}")
        print("🔒 SSL Certificate: Self-signed (you'll see a security warning)")
        print("💡 Click 'Advanced' → 'Proceed to localhost' in your browser")
        print("🛑 Press Ctrl+C to stop the server")
        print()
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n👋 Server stopped")

if __name__ == "__main__":
    main()
