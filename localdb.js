// localdb.js
const API_URL = `https://${window.location.hostname}:8000`; // FastAPI server URL

window.clipitUtils = {
    isValidToken: (token) => /^[\w\-]{3,50}$/.test(token),
    isConfigured: () => true // no Supabase config needed
};

window.clipitDB = {
    async getClipboard(token) {
        const res = await fetch(`${API_URL}/clipboards?token=eq.${encodeURIComponent(token)}`);
        if (!res.ok) throw new Error("Failed to fetch clipboard");
        const data = await res.json();
        return data.length > 0 ? data[0] : null;
    },

    async setClipboard(token, content) {
        const existing = await this.getClipboard(token);
        const body = JSON.stringify({ token, content });

        if (existing) {
            const id = existing.id;
            const res = await fetch(`${API_URL}/clipboards/${id}`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body
            });
            if (!res.ok) throw new Error("Failed to update clipboard");
            return await res.json();
        } else {
            const res = await fetch(`${API_URL}/clipboards`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body
            });
            if (!res.ok) throw new Error("Failed to create clipboard");
            return await res.json();
        }
    },

    // For now, mock real-time updates with polling
    subscribeToChanges(token, callback) {
        let lastContent = null;

        const interval = setInterval(async () => {
            try {
                const data = await this.getClipboard(token);
                if (data && data.content !== lastContent) {
                    lastContent = data.content;
                    callback({ new: data });
                }
            } catch (e) {
                console.error("Polling error:", e);
            }
        }, 2000);

        return interval;
    },

    unsubscribe(intervalId) {
        clearInterval(intervalId);
    }
};
